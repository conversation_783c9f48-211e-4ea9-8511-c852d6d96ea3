{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/ripple-BYgV4oZC.mjs", "../../../../../../node_modules/@angular/material/fesm2022/ripple-loader-BnMiRtmT.mjs", "../../../../../../node_modules/@angular/material/fesm2022/structural-styles-CObeNzjn.mjs", "../../../../../../node_modules/@angular/material/fesm2022/index-BFRo2fUq.mjs"], "sourcesContent": ["import { normalizePassiveListenerOptions, _getEventTarget, Platform } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, InjectionToken, inject, ElementRef, NgZone, Injector, Directive, Input } from '@angular/core';\nimport { isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader } from '@angular/cdk/a11y';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\n\n/** Possible states for a ripple element. */\nvar RippleState;\n(function (RippleState) {\n  RippleState[RippleState[\"FADING_IN\"] = 0] = \"FADING_IN\";\n  RippleState[RippleState[\"VISIBLE\"] = 1] = \"VISIBLE\";\n  RippleState[RippleState[\"FADING_OUT\"] = 2] = \"FADING_OUT\";\n  RippleState[RippleState[\"HIDDEN\"] = 3] = \"HIDDEN\";\n})(RippleState || (RippleState = {}));\n/**\n * Reference to a previously launched ripple element.\n */\nclass RippleRef {\n  _renderer;\n  element;\n  config;\n  _animationForciblyDisabledThroughCss;\n  /** Current state of the ripple. */\n  state = RippleState.HIDDEN;\n  constructor(_renderer, /** Reference to the ripple HTML element. */\n  element, /** Ripple configuration used for the ripple. */\n  config, /* Whether animations are forcibly disabled for ripples through CSS. */\n  _animationForciblyDisabledThroughCss = false) {\n    this._renderer = _renderer;\n    this.element = element;\n    this.config = config;\n    this._animationForciblyDisabledThroughCss = _animationForciblyDisabledThroughCss;\n  }\n  /** Fades out the ripple element. */\n  fadeOut() {\n    this._renderer.fadeOutRipple(this);\n  }\n}\n\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions$1 = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Manages events through delegation so that as few event handlers as possible are bound. */\nclass RippleEventManager {\n  _events = new Map();\n  /** Adds an event handler. */\n  addHandler(ngZone, name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (handlersForEvent) {\n      const handlersForElement = handlersForEvent.get(element);\n      if (handlersForElement) {\n        handlersForElement.add(handler);\n      } else {\n        handlersForEvent.set(element, new Set([handler]));\n      }\n    } else {\n      this._events.set(name, new Map([[element, new Set([handler])]]));\n      ngZone.runOutsideAngular(() => {\n        document.addEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n      });\n    }\n  }\n  /** Removes an event handler. */\n  removeHandler(name, element, handler) {\n    const handlersForEvent = this._events.get(name);\n    if (!handlersForEvent) {\n      return;\n    }\n    const handlersForElement = handlersForEvent.get(element);\n    if (!handlersForElement) {\n      return;\n    }\n    handlersForElement.delete(handler);\n    if (handlersForElement.size === 0) {\n      handlersForEvent.delete(element);\n    }\n    if (handlersForEvent.size === 0) {\n      this._events.delete(name);\n      document.removeEventListener(name, this._delegateEventHandler, passiveCapturingEventOptions$1);\n    }\n  }\n  /** Event handler that is bound and which dispatches the events to the different targets. */\n  _delegateEventHandler = event => {\n    const target = _getEventTarget(event);\n    if (target) {\n      this._events.get(event.type)?.forEach((handlers, element) => {\n        if (element === target || element.contains(target)) {\n          handlers.forEach(handler => handler.handleEvent(event));\n        }\n      });\n    }\n  };\n}\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\nconst defaultRippleAnimationConfig = {\n  enterDuration: 225,\n  exitDuration: 150\n};\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\nconst ignoreMouseEventsTimeout = 800;\n/** Options used to bind a passive capturing event. */\nconst passiveCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Events that signal that the pointer is down. */\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n/** Events that signal that the pointer is up. */\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\nclass _MatRippleStylesLoader {\n  static ɵfac = function _MatRippleStylesLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _MatRippleStylesLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _MatRippleStylesLoader,\n    selectors: [[\"ng-component\"]],\n    hostAttrs: [\"mat-ripple-style-loader\", \"\"],\n    decls: 0,\n    vars: 0,\n    template: function _MatRippleStylesLoader_Template(rf, ctx) {},\n    styles: [\".mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatRippleStylesLoader, [{\n    type: Component,\n    args: [{\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'mat-ripple-style-loader': ''\n      },\n      styles: [\".mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}\\n\"]\n    }]\n  }], null, null);\n})();\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\nclass RippleRenderer {\n  _target;\n  _ngZone;\n  _platform;\n  /** Element where the ripples are being added to. */\n  _containerElement;\n  /** Element which triggers the ripple elements on mouse events. */\n  _triggerElement;\n  /** Whether the pointer is currently down or not. */\n  _isPointerDown = false;\n  /**\n   * Map of currently active ripple references.\n   * The ripple reference is mapped to its element event listeners.\n   * The reason why `| null` is used is that event listeners are added only\n   * when the condition is truthy (see the `_startFadeOutTransition` method).\n   */\n  _activeRipples = new Map();\n  /** Latest non-persistent ripple that was triggered. */\n  _mostRecentTransientRipple;\n  /** Time in milliseconds when the last touchstart event happened. */\n  _lastTouchStartEvent;\n  /** Whether pointer-up event listeners have been registered. */\n  _pointerUpEventsRegistered = false;\n  /**\n   * Cached dimensions of the ripple container. Set when the first\n   * ripple is shown and cleared once no more ripples are visible.\n   */\n  _containerRect;\n  static _eventManager = new RippleEventManager();\n  constructor(_target, _ngZone, elementOrElementRef, _platform, injector) {\n    this._target = _target;\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    // Only do anything if we're on the browser.\n    if (_platform.isBrowser) {\n      this._containerElement = coerceElement(elementOrElementRef);\n    }\n    if (injector) {\n      injector.get(_CdkPrivateStyleLoader).load(_MatRippleStylesLoader);\n    }\n  }\n  /**\n   * Fades in a ripple at the given coordinates.\n   * @param x Coordinate within the element, along the X axis at which to start the ripple.\n   * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n   * @param config Extra ripple options.\n   */\n  fadeInRipple(x, y, config = {}) {\n    const containerRect = this._containerRect = this._containerRect || this._containerElement.getBoundingClientRect();\n    const animationConfig = {\n      ...defaultRippleAnimationConfig,\n      ...config.animation\n    };\n    if (config.centered) {\n      x = containerRect.left + containerRect.width / 2;\n      y = containerRect.top + containerRect.height / 2;\n    }\n    const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n    const offsetX = x - containerRect.left;\n    const offsetY = y - containerRect.top;\n    const enterDuration = animationConfig.enterDuration;\n    const ripple = document.createElement('div');\n    ripple.classList.add('mat-ripple-element');\n    ripple.style.left = `${offsetX - radius}px`;\n    ripple.style.top = `${offsetY - radius}px`;\n    ripple.style.height = `${radius * 2}px`;\n    ripple.style.width = `${radius * 2}px`;\n    // If a custom color has been specified, set it as inline style. If no color is\n    // set, the default color will be applied through the ripple theme styles.\n    if (config.color != null) {\n      ripple.style.backgroundColor = config.color;\n    }\n    ripple.style.transitionDuration = `${enterDuration}ms`;\n    this._containerElement.appendChild(ripple);\n    // By default the browser does not recalculate the styles of dynamically created\n    // ripple elements. This is critical to ensure that the `scale` animates properly.\n    // We enforce a style recalculation by calling `getComputedStyle` and *accessing* a property.\n    // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n    const computedStyles = window.getComputedStyle(ripple);\n    const userTransitionProperty = computedStyles.transitionProperty;\n    const userTransitionDuration = computedStyles.transitionDuration;\n    // Note: We detect whether animation is forcibly disabled through CSS (e.g. through\n    // `transition: none` or `display: none`). This is technically unexpected since animations are\n    // controlled through the animation config, but this exists for backwards compatibility. This\n    // logic does not need to be super accurate since it covers some edge cases which can be easily\n    // avoided by users.\n    const animationForciblyDisabledThroughCss = userTransitionProperty === 'none' ||\n    // Note: The canonical unit for serialized CSS `<time>` properties is seconds. Additionally\n    // some browsers expand the duration for every property (in our case `opacity` and `transform`).\n    userTransitionDuration === '0s' || userTransitionDuration === '0s, 0s' ||\n    // If the container is 0x0, it's likely `display: none`.\n    containerRect.width === 0 && containerRect.height === 0;\n    // Exposed reference to the ripple that will be returned.\n    const rippleRef = new RippleRef(this, ripple, config, animationForciblyDisabledThroughCss);\n    // Start the enter animation by setting the transform/scale to 100%. The animation will\n    // execute as part of this statement because we forced a style recalculation before.\n    // Note: We use a 3d transform here in order to avoid an issue in Safari where\n    // the ripples aren't clipped when inside the shadow DOM (see #24028).\n    ripple.style.transform = 'scale3d(1, 1, 1)';\n    rippleRef.state = RippleState.FADING_IN;\n    if (!config.persistent) {\n      this._mostRecentTransientRipple = rippleRef;\n    }\n    let eventListeners = null;\n    // Do not register the `transition` event listener if fade-in and fade-out duration\n    // are set to zero. The events won't fire anyway and we can save resources here.\n    if (!animationForciblyDisabledThroughCss && (enterDuration || animationConfig.exitDuration)) {\n      this._ngZone.runOutsideAngular(() => {\n        const onTransitionEnd = () => {\n          // Clear the fallback timer since the transition fired correctly.\n          if (eventListeners) {\n            eventListeners.fallbackTimer = null;\n          }\n          clearTimeout(fallbackTimer);\n          this._finishRippleTransition(rippleRef);\n        };\n        const onTransitionCancel = () => this._destroyRipple(rippleRef);\n        // In some cases where there's a higher load on the browser, it can choose not to dispatch\n        // neither `transitionend` nor `transitioncancel` (see b/227356674). This timer serves as a\n        // fallback for such cases so that the ripple doesn't become stuck. We add a 100ms buffer\n        // because timers aren't precise. Note that another approach can be to transition the ripple\n        // to the `VISIBLE` state immediately above and to `FADING_IN` afterwards inside\n        // `transitionstart`. We go with the timer because it's one less event listener and\n        // it's less likely to break existing tests.\n        const fallbackTimer = setTimeout(onTransitionCancel, enterDuration + 100);\n        ripple.addEventListener('transitionend', onTransitionEnd);\n        // If the transition is cancelled (e.g. due to DOM removal), we destroy the ripple\n        // directly as otherwise we would keep it part of the ripple container forever.\n        // https://www.w3.org/TR/css-transitions-1/#:~:text=no%20longer%20in%20the%20document.\n        ripple.addEventListener('transitioncancel', onTransitionCancel);\n        eventListeners = {\n          onTransitionEnd,\n          onTransitionCancel,\n          fallbackTimer\n        };\n      });\n    }\n    // Add the ripple reference to the list of all active ripples.\n    this._activeRipples.set(rippleRef, eventListeners);\n    // In case there is no fade-in transition duration, we need to manually call the transition\n    // end listener because `transitionend` doesn't fire if there is no transition.\n    if (animationForciblyDisabledThroughCss || !enterDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n    return rippleRef;\n  }\n  /** Fades out a ripple reference. */\n  fadeOutRipple(rippleRef) {\n    // For ripples already fading out or hidden, this should be a noop.\n    if (rippleRef.state === RippleState.FADING_OUT || rippleRef.state === RippleState.HIDDEN) {\n      return;\n    }\n    const rippleEl = rippleRef.element;\n    const animationConfig = {\n      ...defaultRippleAnimationConfig,\n      ...rippleRef.config.animation\n    };\n    // This starts the fade-out transition and will fire the transition end listener that\n    // removes the ripple element from the DOM.\n    rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n    rippleEl.style.opacity = '0';\n    rippleRef.state = RippleState.FADING_OUT;\n    // In case there is no fade-out transition duration, we need to manually call the\n    // transition end listener because `transitionend` doesn't fire if there is no transition.\n    if (rippleRef._animationForciblyDisabledThroughCss || !animationConfig.exitDuration) {\n      this._finishRippleTransition(rippleRef);\n    }\n  }\n  /** Fades out all currently active ripples. */\n  fadeOutAll() {\n    this._getActiveRipples().forEach(ripple => ripple.fadeOut());\n  }\n  /** Fades out all currently active non-persistent ripples. */\n  fadeOutAllNonPersistent() {\n    this._getActiveRipples().forEach(ripple => {\n      if (!ripple.config.persistent) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  /** Sets up the trigger event listeners */\n  setupTriggerEvents(elementOrElementRef) {\n    const element = coerceElement(elementOrElementRef);\n    if (!this._platform.isBrowser || !element || element === this._triggerElement) {\n      return;\n    }\n    // Remove all previously registered event listeners from the trigger element.\n    this._removeTriggerEvents();\n    this._triggerElement = element;\n    // Use event delegation for the trigger events since they're\n    // set up during creation and are performance-sensitive.\n    pointerDownEvents.forEach(type => {\n      RippleRenderer._eventManager.addHandler(this._ngZone, type, element, this);\n    });\n  }\n  /**\n   * Handles all registered events.\n   * @docs-private\n   */\n  handleEvent(event) {\n    if (event.type === 'mousedown') {\n      this._onMousedown(event);\n    } else if (event.type === 'touchstart') {\n      this._onTouchStart(event);\n    } else {\n      this._onPointerUp();\n    }\n    // If pointer-up events haven't been registered yet, do so now.\n    // We do this on-demand in order to reduce the total number of event listeners\n    // registered by the ripples, which speeds up the rendering time for large UIs.\n    if (!this._pointerUpEventsRegistered) {\n      // The events for hiding the ripple are bound directly on the trigger, because:\n      // 1. Some of them occur frequently (e.g. `mouseleave`) and any advantage we get from\n      // delegation will be diminished by having to look through all the data structures often.\n      // 2. They aren't as performance-sensitive, because they're bound only after the user\n      // has interacted with an element.\n      this._ngZone.runOutsideAngular(() => {\n        pointerUpEvents.forEach(type => {\n          this._triggerElement.addEventListener(type, this, passiveCapturingEventOptions);\n        });\n      });\n      this._pointerUpEventsRegistered = true;\n    }\n  }\n  /** Method that will be called if the fade-in or fade-in transition completed. */\n  _finishRippleTransition(rippleRef) {\n    if (rippleRef.state === RippleState.FADING_IN) {\n      this._startFadeOutTransition(rippleRef);\n    } else if (rippleRef.state === RippleState.FADING_OUT) {\n      this._destroyRipple(rippleRef);\n    }\n  }\n  /**\n   * Starts the fade-out transition of the given ripple if it's not persistent and the pointer\n   * is not held down anymore.\n   */\n  _startFadeOutTransition(rippleRef) {\n    const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n    const {\n      persistent\n    } = rippleRef.config;\n    rippleRef.state = RippleState.VISIBLE;\n    // When the timer runs out while the user has kept their pointer down, we want to\n    // keep only the persistent ripples and the latest transient ripple. We do this,\n    // because we don't want stacked transient ripples to appear after their enter\n    // animation has finished.\n    if (!persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n      rippleRef.fadeOut();\n    }\n  }\n  /** Destroys the given ripple by removing it from the DOM and updating its state. */\n  _destroyRipple(rippleRef) {\n    const eventListeners = this._activeRipples.get(rippleRef) ?? null;\n    this._activeRipples.delete(rippleRef);\n    // Clear out the cached bounding rect if we have no more ripples.\n    if (!this._activeRipples.size) {\n      this._containerRect = null;\n    }\n    // If the current ref is the most recent transient ripple, unset it\n    // avoid memory leaks.\n    if (rippleRef === this._mostRecentTransientRipple) {\n      this._mostRecentTransientRipple = null;\n    }\n    rippleRef.state = RippleState.HIDDEN;\n    if (eventListeners !== null) {\n      rippleRef.element.removeEventListener('transitionend', eventListeners.onTransitionEnd);\n      rippleRef.element.removeEventListener('transitioncancel', eventListeners.onTransitionCancel);\n      if (eventListeners.fallbackTimer !== null) {\n        clearTimeout(eventListeners.fallbackTimer);\n      }\n    }\n    rippleRef.element.remove();\n  }\n  /** Function being called whenever the trigger is being pressed using mouse. */\n  _onMousedown(event) {\n    // Screen readers will fire fake mouse events for space/enter. Skip launching a\n    // ripple in this case for consistency with the non-screen-reader experience.\n    const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n    const isSyntheticEvent = this._lastTouchStartEvent && Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n    if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n      this._isPointerDown = true;\n      this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n    }\n  }\n  /** Function being called whenever the trigger is being pressed using touch. */\n  _onTouchStart(event) {\n    if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n      // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n      // events will launch a second ripple if we don't ignore mouse events for a specific\n      // time after a touchstart event.\n      this._lastTouchStartEvent = Date.now();\n      this._isPointerDown = true;\n      // Use `changedTouches` so we skip any touches where the user put\n      // their finger down, but used another finger to tap the element again.\n      const touches = event.changedTouches;\n      // According to the typings the touches should always be defined, but in some cases\n      // the browser appears to not assign them in tests which leads to flakes.\n      if (touches) {\n        for (let i = 0; i < touches.length; i++) {\n          this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n        }\n      }\n    }\n  }\n  /** Function being called whenever the trigger is being released. */\n  _onPointerUp() {\n    if (!this._isPointerDown) {\n      return;\n    }\n    this._isPointerDown = false;\n    // Fade-out all ripples that are visible and not persistent.\n    this._getActiveRipples().forEach(ripple => {\n      // By default, only ripples that are completely visible will fade out on pointer release.\n      // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n      const isVisible = ripple.state === RippleState.VISIBLE || ripple.config.terminateOnPointerUp && ripple.state === RippleState.FADING_IN;\n      if (!ripple.config.persistent && isVisible) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  _getActiveRipples() {\n    return Array.from(this._activeRipples.keys());\n  }\n  /** Removes previously registered event listeners from the trigger element. */\n  _removeTriggerEvents() {\n    const trigger = this._triggerElement;\n    if (trigger) {\n      pointerDownEvents.forEach(type => RippleRenderer._eventManager.removeHandler(type, trigger, this));\n      if (this._pointerUpEventsRegistered) {\n        pointerUpEvents.forEach(type => trigger.removeEventListener(type, this, passiveCapturingEventOptions));\n        this._pointerUpEventsRegistered = false;\n      }\n    }\n  }\n}\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\nfunction distanceToFurthestCorner(x, y, rect) {\n  const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n  const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n  return Math.sqrt(distX * distX + distY * distY);\n}\n\n/** Injection token that can be used to specify the global ripple options. */\nconst MAT_RIPPLE_GLOBAL_OPTIONS = new InjectionToken('mat-ripple-global-options');\nclass MatRipple {\n  _elementRef = inject(ElementRef);\n  _animationsDisabled = _animationsDisabled();\n  /** Custom color for all ripples. */\n  color;\n  /** Whether the ripples should be visible outside the component's bounds. */\n  unbounded;\n  /**\n   * Whether the ripple always originates from the center of the host element's bounds, rather\n   * than originating from the location of the click event.\n   */\n  centered;\n  /**\n   * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n   * will be the distance from the center of the ripple to the furthest corner of the host element's\n   * bounding rectangle.\n   */\n  radius = 0;\n  /**\n   * Configuration for the ripple animation. Allows modifying the enter and exit animation\n   * duration of the ripples. The animation durations will be overwritten if the\n   * `NoopAnimationsModule` is being used.\n   */\n  animation;\n  /**\n   * Whether click events will not trigger the ripple. Ripples can be still launched manually\n   * by using the `launch()` method.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    if (value) {\n      this.fadeOutAllNonPersistent();\n    }\n    this._disabled = value;\n    this._setupTriggerEventsIfEnabled();\n  }\n  _disabled = false;\n  /**\n   * The element that triggers the ripple when click events are received.\n   * Defaults to the directive's host element.\n   */\n  get trigger() {\n    return this._trigger || this._elementRef.nativeElement;\n  }\n  set trigger(trigger) {\n    this._trigger = trigger;\n    this._setupTriggerEventsIfEnabled();\n  }\n  _trigger;\n  /** Renderer for the ripple DOM manipulations. */\n  _rippleRenderer;\n  /** Options that are set globally for all ripples. */\n  _globalOptions;\n  /** @docs-private Whether ripple directive is initialized and the input bindings are set. */\n  _isInitialized = false;\n  constructor() {\n    const ngZone = inject(NgZone);\n    const platform = inject(Platform);\n    const globalOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n      optional: true\n    });\n    const injector = inject(Injector);\n    // Note: cannot use `inject()` here, because this class\n    // gets instantiated manually in the ripple loader.\n    this._globalOptions = globalOptions || {};\n    this._rippleRenderer = new RippleRenderer(this, ngZone, this._elementRef, platform, injector);\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n    this._setupTriggerEventsIfEnabled();\n  }\n  ngOnDestroy() {\n    this._rippleRenderer._removeTriggerEvents();\n  }\n  /** Fades out all currently showing ripple elements. */\n  fadeOutAll() {\n    this._rippleRenderer.fadeOutAll();\n  }\n  /** Fades out all currently showing non-persistent ripple elements. */\n  fadeOutAllNonPersistent() {\n    this._rippleRenderer.fadeOutAllNonPersistent();\n  }\n  /**\n   * Ripple configuration from the directive's input values.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleConfig() {\n    return {\n      centered: this.centered,\n      radius: this.radius,\n      color: this.color,\n      animation: {\n        ...this._globalOptions.animation,\n        ...(this._animationsDisabled ? {\n          enterDuration: 0,\n          exitDuration: 0\n        } : {}),\n        ...this.animation\n      },\n      terminateOnPointerUp: this._globalOptions.terminateOnPointerUp\n    };\n  }\n  /**\n   * Whether ripples on pointer-down are disabled or not.\n   * @docs-private Implemented as part of RippleTarget\n   */\n  get rippleDisabled() {\n    return this.disabled || !!this._globalOptions.disabled;\n  }\n  /** Sets up the trigger event listeners if ripples are enabled. */\n  _setupTriggerEventsIfEnabled() {\n    if (!this.disabled && this._isInitialized) {\n      this._rippleRenderer.setupTriggerEvents(this.trigger);\n    }\n  }\n  /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n  launch(configOrX, y = 0, config) {\n    if (typeof configOrX === 'number') {\n      return this._rippleRenderer.fadeInRipple(configOrX, y, {\n        ...this.rippleConfig,\n        ...config\n      });\n    } else {\n      return this._rippleRenderer.fadeInRipple(0, 0, {\n        ...this.rippleConfig,\n        ...configOrX\n      });\n    }\n  }\n  static ɵfac = function MatRipple_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatRipple)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatRipple,\n    selectors: [[\"\", \"mat-ripple\", \"\"], [\"\", \"matRipple\", \"\"]],\n    hostAttrs: [1, \"mat-ripple\"],\n    hostVars: 2,\n    hostBindings: function MatRipple_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-ripple-unbounded\", ctx.unbounded);\n      }\n    },\n    inputs: {\n      color: [0, \"matRippleColor\", \"color\"],\n      unbounded: [0, \"matRippleUnbounded\", \"unbounded\"],\n      centered: [0, \"matRippleCentered\", \"centered\"],\n      radius: [0, \"matRippleRadius\", \"radius\"],\n      animation: [0, \"matRippleAnimation\", \"animation\"],\n      disabled: [0, \"matRippleDisabled\", \"disabled\"],\n      trigger: [0, \"matRippleTrigger\", \"trigger\"]\n    },\n    exportAs: [\"matRipple\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRipple, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-ripple], [matRipple]',\n      exportAs: 'matRipple',\n      host: {\n        'class': 'mat-ripple',\n        '[class.mat-ripple-unbounded]': 'unbounded'\n      }\n    }]\n  }], () => [], {\n    color: [{\n      type: Input,\n      args: ['matRippleColor']\n    }],\n    unbounded: [{\n      type: Input,\n      args: ['matRippleUnbounded']\n    }],\n    centered: [{\n      type: Input,\n      args: ['matRippleCentered']\n    }],\n    radius: [{\n      type: Input,\n      args: ['matRippleRadius']\n    }],\n    animation: [{\n      type: Input,\n      args: ['matRippleAnimation']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['matRippleDisabled']\n    }],\n    trigger: [{\n      type: Input,\n      args: ['matRippleTrigger']\n    }]\n  });\n})();\nexport { MatRipple as M, RippleRenderer as R, MAT_RIPPLE_GLOBAL_OPTIONS as a, RippleState as b, RippleRef as c, defaultRippleAnimationConfig as d };\n", "import * as i0 from '@angular/core';\nimport { inject, DOCUMENT, NgZone, Injector, RendererFactory2, Injectable } from '@angular/core';\nimport { Platform, _getEventTarget } from '@angular/cdk/platform';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { a as MAT_RIPPLE_GLOBAL_OPTIONS, R as RippleRenderer, d as defaultRippleAnimationConfig } from './ripple-BYgV4oZC.mjs';\n\n/** The options for the MatRippleLoader's event listeners. */\nconst eventListenerOptions = {\n  capture: true\n};\n/**\n * The events that should trigger the initialization of the ripple.\n * Note that we use `mousedown`, rather than `click`, for mouse devices because\n * we can't rely on `mouseenter` in the shadow DOM and `click` happens too late.\n */\nconst rippleInteractionEvents = ['focus', 'mousedown', 'mouseenter', 'touchstart'];\n/** The attribute attached to a component whose ripple has not yet been initialized. */\nconst matRippleUninitialized = 'mat-ripple-loader-uninitialized';\n/** Additional classes that should be added to the ripple when it is rendered. */\nconst matRippleClassName = 'mat-ripple-loader-class-name';\n/** Whether the ripple should be centered. */\nconst matRippleCentered = 'mat-ripple-loader-centered';\n/** Whether the ripple should be disabled. */\nconst matRippleDisabled = 'mat-ripple-loader-disabled';\n/**\n * Handles attaching ripples on demand.\n *\n * This service allows us to avoid eagerly creating & attaching MatRipples.\n * It works by creating & attaching a ripple only when a component is first interacted with.\n *\n * @docs-private\n */\nclass MatRippleLoader {\n  _document = inject(DOCUMENT);\n  _animationsDisabled = _animationsDisabled();\n  _globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n    optional: true\n  });\n  _platform = inject(Platform);\n  _ngZone = inject(NgZone);\n  _injector = inject(Injector);\n  _eventCleanups;\n  _hosts = new Map();\n  constructor() {\n    const renderer = inject(RendererFactory2).createRenderer(null, null);\n    this._eventCleanups = this._ngZone.runOutsideAngular(() => rippleInteractionEvents.map(name => renderer.listen(this._document, name, this._onInteraction, eventListenerOptions)));\n  }\n  ngOnDestroy() {\n    const hosts = this._hosts.keys();\n    for (const host of hosts) {\n      this.destroyRipple(host);\n    }\n    this._eventCleanups.forEach(cleanup => cleanup());\n  }\n  /**\n   * Configures the ripple that will be rendered by the ripple loader.\n   *\n   * Stores the given information about how the ripple should be configured on the host\n   * element so that it can later be retrived & used when the ripple is actually created.\n   */\n  configureRipple(host, config) {\n    // Indicates that the ripple has not yet been rendered for this component.\n    host.setAttribute(matRippleUninitialized, this._globalRippleOptions?.namespace ?? '');\n    // Store the additional class name(s) that should be added to the ripple element.\n    if (config.className || !host.hasAttribute(matRippleClassName)) {\n      host.setAttribute(matRippleClassName, config.className || '');\n    }\n    // Store whether the ripple should be centered.\n    if (config.centered) {\n      host.setAttribute(matRippleCentered, '');\n    }\n    if (config.disabled) {\n      host.setAttribute(matRippleDisabled, '');\n    }\n  }\n  /** Sets the disabled state on the ripple instance corresponding to the given host element. */\n  setDisabled(host, disabled) {\n    const ripple = this._hosts.get(host);\n    // If the ripple has already been instantiated, just disable it.\n    if (ripple) {\n      ripple.target.rippleDisabled = disabled;\n      if (!disabled && !ripple.hasSetUpEvents) {\n        ripple.hasSetUpEvents = true;\n        ripple.renderer.setupTriggerEvents(host);\n      }\n    } else if (disabled) {\n      // Otherwise, set an attribute so we know what the\n      // disabled state should be when the ripple is initialized.\n      host.setAttribute(matRippleDisabled, '');\n    } else {\n      host.removeAttribute(matRippleDisabled);\n    }\n  }\n  /**\n   * Handles creating and attaching component internals\n   * when a component is initially interacted with.\n   */\n  _onInteraction = event => {\n    const eventTarget = _getEventTarget(event);\n    if (eventTarget instanceof HTMLElement) {\n      // TODO(wagnermaciel): Consider batching these events to improve runtime performance.\n      const element = eventTarget.closest(`[${matRippleUninitialized}=\"${this._globalRippleOptions?.namespace ?? ''}\"]`);\n      if (element) {\n        this._createRipple(element);\n      }\n    }\n  };\n  /** Creates a MatRipple and appends it to the given element. */\n  _createRipple(host) {\n    if (!this._document || this._hosts.has(host)) {\n      return;\n    }\n    // Create the ripple element.\n    host.querySelector('.mat-ripple')?.remove();\n    const rippleEl = this._document.createElement('span');\n    rippleEl.classList.add('mat-ripple', host.getAttribute(matRippleClassName));\n    host.append(rippleEl);\n    const globalOptions = this._globalRippleOptions;\n    const enterDuration = this._animationsDisabled ? 0 : globalOptions?.animation?.enterDuration ?? defaultRippleAnimationConfig.enterDuration;\n    const exitDuration = this._animationsDisabled ? 0 : globalOptions?.animation?.exitDuration ?? defaultRippleAnimationConfig.exitDuration;\n    const target = {\n      rippleDisabled: this._animationsDisabled || globalOptions?.disabled || host.hasAttribute(matRippleDisabled),\n      rippleConfig: {\n        centered: host.hasAttribute(matRippleCentered),\n        terminateOnPointerUp: globalOptions?.terminateOnPointerUp,\n        animation: {\n          enterDuration,\n          exitDuration\n        }\n      }\n    };\n    const renderer = new RippleRenderer(target, this._ngZone, rippleEl, this._platform, this._injector);\n    const hasSetUpEvents = !target.rippleDisabled;\n    if (hasSetUpEvents) {\n      renderer.setupTriggerEvents(host);\n    }\n    this._hosts.set(host, {\n      target,\n      renderer,\n      hasSetUpEvents\n    });\n    host.removeAttribute(matRippleUninitialized);\n  }\n  destroyRipple(host) {\n    const ripple = this._hosts.get(host);\n    if (ripple) {\n      ripple.renderer._removeTriggerEvents();\n      this._hosts.delete(host);\n    }\n  }\n  static ɵfac = function MatRippleLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatRippleLoader)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatRippleLoader,\n    factory: MatRippleLoader.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleLoader, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { MatRippleLoader as M };\n", "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\n\n/**\n * Component used to load structural styles for focus indicators.\n * @docs-private\n */\nclass _StructuralStylesLoader {\n  static ɵfac = function _StructuralStylesLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _StructuralStylesLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _StructuralStylesLoader,\n    selectors: [[\"structural-styles\"]],\n    decls: 0,\n    vars: 0,\n    template: function _StructuralStylesLoader_Template(rf, ctx) {},\n    styles: [\".mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\\\"\\\"}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_StructuralStylesLoader, [{\n    type: Component,\n    args: [{\n      selector: 'structural-styles',\n      encapsulation: ViewEncapsulation.None,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\\\"\\\"}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}\\n\"]\n    }]\n  }], null, null);\n})();\nexport { _StructuralStylesLoader as _ };\n", "import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatRipple } from './ripple-BYgV4oZC.mjs';\nclass MatRippleModule {\n  static ɵfac = function MatRippleModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatRippleModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatRippleModule,\n    imports: [MatCommonModule, MatRipple],\n    exports: [MatRipple, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatRipple],\n      exports: [MatR<PERSON>ple, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MatRippleModule as M };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAI;AAAA,CACH,SAAUA,cAAa;AACtB,EAAAA,aAAYA,aAAY,WAAW,IAAI,CAAC,IAAI;AAC5C,EAAAA,aAAYA,aAAY,SAAS,IAAI,CAAC,IAAI;AAC1C,EAAAA,aAAYA,aAAY,YAAY,IAAI,CAAC,IAAI;AAC7C,EAAAA,aAAYA,aAAY,QAAQ,IAAI,CAAC,IAAI;AAC3C,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAIpC,IAAM,YAAN,MAAgB;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,QAAQ,YAAY;AAAA,EACpB,YAAY,WACZ,SACA,QACA,uCAAuC,OAAO;AAC5C,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,uCAAuC;AAAA,EAC9C;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,UAAU,cAAc,IAAI;AAAA,EACnC;AACF;AAGA,IAAM,iCAAiC,gCAAgC;AAAA,EACrE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAED,IAAM,qBAAN,MAAyB;AAAA,EACvB,UAAU,oBAAI,IAAI;AAAA;AAAA,EAElB,WAAW,QAAQ,MAAM,SAAS,SAAS;AACzC,UAAM,mBAAmB,KAAK,QAAQ,IAAI,IAAI;AAC9C,QAAI,kBAAkB;AACpB,YAAM,qBAAqB,iBAAiB,IAAI,OAAO;AACvD,UAAI,oBAAoB;AACtB,2BAAmB,IAAI,OAAO;AAAA,MAChC,OAAO;AACL,yBAAiB,IAAI,SAAS,oBAAI,IAAI,CAAC,OAAO,CAAC,CAAC;AAAA,MAClD;AAAA,IACF,OAAO;AACL,WAAK,QAAQ,IAAI,MAAM,oBAAI,IAAI,CAAC,CAAC,SAAS,oBAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,aAAO,kBAAkB,MAAM;AAC7B,iBAAS,iBAAiB,MAAM,KAAK,uBAAuB,8BAA8B;AAAA,MAC5F,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,MAAM,SAAS,SAAS;AACpC,UAAM,mBAAmB,KAAK,QAAQ,IAAI,IAAI;AAC9C,QAAI,CAAC,kBAAkB;AACrB;AAAA,IACF;AACA,UAAM,qBAAqB,iBAAiB,IAAI,OAAO;AACvD,QAAI,CAAC,oBAAoB;AACvB;AAAA,IACF;AACA,uBAAmB,OAAO,OAAO;AACjC,QAAI,mBAAmB,SAAS,GAAG;AACjC,uBAAiB,OAAO,OAAO;AAAA,IACjC;AACA,QAAI,iBAAiB,SAAS,GAAG;AAC/B,WAAK,QAAQ,OAAO,IAAI;AACxB,eAAS,oBAAoB,MAAM,KAAK,uBAAuB,8BAA8B;AAAA,IAC/F;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB,WAAS;AAC/B,UAAM,SAAS,gBAAgB,KAAK;AACpC,QAAI,QAAQ;AACV,WAAK,QAAQ,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,UAAU,YAAY;AAC3D,YAAI,YAAY,UAAU,QAAQ,SAAS,MAAM,GAAG;AAClD,mBAAS,QAAQ,aAAW,QAAQ,YAAY,KAAK,CAAC;AAAA,QACxD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAMA,IAAM,+BAA+B;AAAA,EACnC,eAAe;AAAA,EACf,cAAc;AAChB;AAKA,IAAM,2BAA2B;AAEjC,IAAM,+BAA+B,gCAAgC;AAAA,EACnE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAED,IAAM,oBAAoB,CAAC,aAAa,YAAY;AAEpD,IAAM,kBAAkB,CAAC,WAAW,cAAc,YAAY,aAAa;AAC3E,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,CAAC,2BAA2B,EAAE;AAAA,IACzC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAAA,IAAC;AAAA,IAC7D,QAAQ,CAAC,6jBAA6jB;AAAA,IACtkB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,2BAA2B;AAAA,MAC7B;AAAA,MACA,QAAQ,CAAC,6jBAA6jB;AAAA,IACxkB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjB,iBAAiB,oBAAI,IAAI;AAAA;AAAA,EAEzB;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,6BAA6B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B;AAAA,EACA,OAAO,gBAAgB,IAAI,mBAAmB;AAAA,EAC9C,YAAY,SAAS,SAAS,qBAAqB,WAAW,UAAU;AACtE,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,YAAY;AAEjB,QAAI,UAAU,WAAW;AACvB,WAAK,oBAAoB,cAAc,mBAAmB;AAAA,IAC5D;AACA,QAAI,UAAU;AACZ,eAAS,IAAI,sBAAsB,EAAE,KAAK,sBAAsB;AAAA,IAClE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,GAAG,GAAG,SAAS,CAAC,GAAG;AAC9B,UAAM,gBAAgB,KAAK,iBAAiB,KAAK,kBAAkB,KAAK,kBAAkB,sBAAsB;AAChH,UAAM,kBAAkB,kCACnB,+BACA,OAAO;AAEZ,QAAI,OAAO,UAAU;AACnB,UAAI,cAAc,OAAO,cAAc,QAAQ;AAC/C,UAAI,cAAc,MAAM,cAAc,SAAS;AAAA,IACjD;AACA,UAAM,SAAS,OAAO,UAAU,yBAAyB,GAAG,GAAG,aAAa;AAC5E,UAAM,UAAU,IAAI,cAAc;AAClC,UAAM,UAAU,IAAI,cAAc;AAClC,UAAM,gBAAgB,gBAAgB;AACtC,UAAM,SAAS,SAAS,cAAc,KAAK;AAC3C,WAAO,UAAU,IAAI,oBAAoB;AACzC,WAAO,MAAM,OAAO,GAAG,UAAU,MAAM;AACvC,WAAO,MAAM,MAAM,GAAG,UAAU,MAAM;AACtC,WAAO,MAAM,SAAS,GAAG,SAAS,CAAC;AACnC,WAAO,MAAM,QAAQ,GAAG,SAAS,CAAC;AAGlC,QAAI,OAAO,SAAS,MAAM;AACxB,aAAO,MAAM,kBAAkB,OAAO;AAAA,IACxC;AACA,WAAO,MAAM,qBAAqB,GAAG,aAAa;AAClD,SAAK,kBAAkB,YAAY,MAAM;AAKzC,UAAM,iBAAiB,OAAO,iBAAiB,MAAM;AACrD,UAAM,yBAAyB,eAAe;AAC9C,UAAM,yBAAyB,eAAe;AAM9C,UAAM,sCAAsC,2BAA2B;AAAA;AAAA,IAGvE,2BAA2B,QAAQ,2BAA2B;AAAA,IAE9D,cAAc,UAAU,KAAK,cAAc,WAAW;AAEtD,UAAM,YAAY,IAAI,UAAU,MAAM,QAAQ,QAAQ,mCAAmC;AAKzF,WAAO,MAAM,YAAY;AACzB,cAAU,QAAQ,YAAY;AAC9B,QAAI,CAAC,OAAO,YAAY;AACtB,WAAK,6BAA6B;AAAA,IACpC;AACA,QAAI,iBAAiB;AAGrB,QAAI,CAAC,wCAAwC,iBAAiB,gBAAgB,eAAe;AAC3F,WAAK,QAAQ,kBAAkB,MAAM;AACnC,cAAM,kBAAkB,MAAM;AAE5B,cAAI,gBAAgB;AAClB,2BAAe,gBAAgB;AAAA,UACjC;AACA,uBAAa,aAAa;AAC1B,eAAK,wBAAwB,SAAS;AAAA,QACxC;AACA,cAAM,qBAAqB,MAAM,KAAK,eAAe,SAAS;AAQ9D,cAAM,gBAAgB,WAAW,oBAAoB,gBAAgB,GAAG;AACxE,eAAO,iBAAiB,iBAAiB,eAAe;AAIxD,eAAO,iBAAiB,oBAAoB,kBAAkB;AAC9D,yBAAiB;AAAA,UACf;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,SAAK,eAAe,IAAI,WAAW,cAAc;AAGjD,QAAI,uCAAuC,CAAC,eAAe;AACzD,WAAK,wBAAwB,SAAS;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,cAAc,WAAW;AAEvB,QAAI,UAAU,UAAU,YAAY,cAAc,UAAU,UAAU,YAAY,QAAQ;AACxF;AAAA,IACF;AACA,UAAM,WAAW,UAAU;AAC3B,UAAM,kBAAkB,kCACnB,+BACA,UAAU,OAAO;AAItB,aAAS,MAAM,qBAAqB,GAAG,gBAAgB,YAAY;AACnE,aAAS,MAAM,UAAU;AACzB,cAAU,QAAQ,YAAY;AAG9B,QAAI,UAAU,wCAAwC,CAAC,gBAAgB,cAAc;AACnF,WAAK,wBAAwB,SAAS;AAAA,IACxC;AAAA,EACF;AAAA;AAAA,EAEA,aAAa;AACX,SAAK,kBAAkB,EAAE,QAAQ,YAAU,OAAO,QAAQ,CAAC;AAAA,EAC7D;AAAA;AAAA,EAEA,0BAA0B;AACxB,SAAK,kBAAkB,EAAE,QAAQ,YAAU;AACzC,UAAI,CAAC,OAAO,OAAO,YAAY;AAC7B,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,mBAAmB,qBAAqB;AACtC,UAAM,UAAU,cAAc,mBAAmB;AACjD,QAAI,CAAC,KAAK,UAAU,aAAa,CAAC,WAAW,YAAY,KAAK,iBAAiB;AAC7E;AAAA,IACF;AAEA,SAAK,qBAAqB;AAC1B,SAAK,kBAAkB;AAGvB,sBAAkB,QAAQ,UAAQ;AAChC,sBAAe,cAAc,WAAW,KAAK,SAAS,MAAM,SAAS,IAAI;AAAA,IAC3E,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,OAAO;AACjB,QAAI,MAAM,SAAS,aAAa;AAC9B,WAAK,aAAa,KAAK;AAAA,IACzB,WAAW,MAAM,SAAS,cAAc;AACtC,WAAK,cAAc,KAAK;AAAA,IAC1B,OAAO;AACL,WAAK,aAAa;AAAA,IACpB;AAIA,QAAI,CAAC,KAAK,4BAA4B;AAMpC,WAAK,QAAQ,kBAAkB,MAAM;AACnC,wBAAgB,QAAQ,UAAQ;AAC9B,eAAK,gBAAgB,iBAAiB,MAAM,MAAM,4BAA4B;AAAA,QAChF,CAAC;AAAA,MACH,CAAC;AACD,WAAK,6BAA6B;AAAA,IACpC;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB,WAAW;AACjC,QAAI,UAAU,UAAU,YAAY,WAAW;AAC7C,WAAK,wBAAwB,SAAS;AAAA,IACxC,WAAW,UAAU,UAAU,YAAY,YAAY;AACrD,WAAK,eAAe,SAAS;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,WAAW;AACjC,UAAM,8BAA8B,cAAc,KAAK;AACvD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,UAAU;AACd,cAAU,QAAQ,YAAY;AAK9B,QAAI,CAAC,eAAe,CAAC,+BAA+B,CAAC,KAAK,iBAAiB;AACzE,gBAAU,QAAQ;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,WAAW;AACxB,UAAM,iBAAiB,KAAK,eAAe,IAAI,SAAS,KAAK;AAC7D,SAAK,eAAe,OAAO,SAAS;AAEpC,QAAI,CAAC,KAAK,eAAe,MAAM;AAC7B,WAAK,iBAAiB;AAAA,IACxB;AAGA,QAAI,cAAc,KAAK,4BAA4B;AACjD,WAAK,6BAA6B;AAAA,IACpC;AACA,cAAU,QAAQ,YAAY;AAC9B,QAAI,mBAAmB,MAAM;AAC3B,gBAAU,QAAQ,oBAAoB,iBAAiB,eAAe,eAAe;AACrF,gBAAU,QAAQ,oBAAoB,oBAAoB,eAAe,kBAAkB;AAC3F,UAAI,eAAe,kBAAkB,MAAM;AACzC,qBAAa,eAAe,aAAa;AAAA,MAC3C;AAAA,IACF;AACA,cAAU,QAAQ,OAAO;AAAA,EAC3B;AAAA;AAAA,EAEA,aAAa,OAAO;AAGlB,UAAM,kBAAkB,gCAAgC,KAAK;AAC7D,UAAM,mBAAmB,KAAK,wBAAwB,KAAK,IAAI,IAAI,KAAK,uBAAuB;AAC/F,QAAI,CAAC,KAAK,QAAQ,kBAAkB,CAAC,mBAAmB,CAAC,kBAAkB;AACzE,WAAK,iBAAiB;AACtB,WAAK,aAAa,MAAM,SAAS,MAAM,SAAS,KAAK,QAAQ,YAAY;AAAA,IAC3E;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,OAAO;AACnB,QAAI,CAAC,KAAK,QAAQ,kBAAkB,CAAC,iCAAiC,KAAK,GAAG;AAI5E,WAAK,uBAAuB,KAAK,IAAI;AACrC,WAAK,iBAAiB;AAGtB,YAAM,UAAU,MAAM;AAGtB,UAAI,SAAS;AACX,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,eAAK,aAAa,QAAQ,CAAC,EAAE,SAAS,QAAQ,CAAC,EAAE,SAAS,KAAK,QAAQ,YAAY;AAAA,QACrF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,eAAe;AACb,QAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,IACF;AACA,SAAK,iBAAiB;AAEtB,SAAK,kBAAkB,EAAE,QAAQ,YAAU;AAGzC,YAAM,YAAY,OAAO,UAAU,YAAY,WAAW,OAAO,OAAO,wBAAwB,OAAO,UAAU,YAAY;AAC7H,UAAI,CAAC,OAAO,OAAO,cAAc,WAAW;AAC1C,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,WAAO,MAAM,KAAK,KAAK,eAAe,KAAK,CAAC;AAAA,EAC9C;AAAA;AAAA,EAEA,uBAAuB;AACrB,UAAM,UAAU,KAAK;AACrB,QAAI,SAAS;AACX,wBAAkB,QAAQ,UAAQ,gBAAe,cAAc,cAAc,MAAM,SAAS,IAAI,CAAC;AACjG,UAAI,KAAK,4BAA4B;AACnC,wBAAgB,QAAQ,UAAQ,QAAQ,oBAAoB,MAAM,MAAM,4BAA4B,CAAC;AACrG,aAAK,6BAA6B;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AACF;AAIA,SAAS,yBAAyB,GAAG,GAAG,MAAM;AAC5C,QAAM,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK,CAAC;AACxE,QAAM,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,GAAG,GAAG,KAAK,IAAI,IAAI,KAAK,MAAM,CAAC;AACxE,SAAO,KAAK,KAAK,QAAQ,QAAQ,QAAQ,KAAK;AAChD;AAGA,IAAM,4BAA4B,IAAI,eAAe,2BAA2B;AAChF,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,cAAc,OAAO,UAAU;AAAA,EAC/B,sBAAsB,oBAAoB;AAAA;AAAA,EAE1C;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,QAAI,OAAO;AACT,WAAK,wBAAwB;AAAA,IAC/B;AACA,SAAK,YAAY;AACjB,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,IAAI,UAAU;AACZ,WAAO,KAAK,YAAY,KAAK,YAAY;AAAA,EAC3C;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,SAAK,WAAW;AAChB,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,iBAAiB;AAAA,EACjB,cAAc;AACZ,UAAM,SAAS,OAAO,MAAM;AAC5B,UAAM,WAAW,OAAO,QAAQ;AAChC,UAAM,gBAAgB,OAAO,2BAA2B;AAAA,MACtD,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,WAAW,OAAO,QAAQ;AAGhC,SAAK,iBAAiB,iBAAiB,CAAC;AACxC,SAAK,kBAAkB,IAAI,eAAe,MAAM,QAAQ,KAAK,aAAa,UAAU,QAAQ;AAAA,EAC9F;AAAA,EACA,WAAW;AACT,SAAK,iBAAiB;AACtB,SAAK,6BAA6B;AAAA,EACpC;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB,qBAAqB;AAAA,EAC5C;AAAA;AAAA,EAEA,aAAa;AACX,SAAK,gBAAgB,WAAW;AAAA,EAClC;AAAA;AAAA,EAEA,0BAA0B;AACxB,SAAK,gBAAgB,wBAAwB;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,eAAe;AACjB,WAAO;AAAA,MACL,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,WAAW,iDACN,KAAK,eAAe,YACnB,KAAK,sBAAsB;AAAA,QAC7B,eAAe;AAAA,QACf,cAAc;AAAA,MAChB,IAAI,CAAC,IACF,KAAK;AAAA,MAEV,sBAAsB,KAAK,eAAe;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,iBAAiB;AACnB,WAAO,KAAK,YAAY,CAAC,CAAC,KAAK,eAAe;AAAA,EAChD;AAAA;AAAA,EAEA,+BAA+B;AAC7B,QAAI,CAAC,KAAK,YAAY,KAAK,gBAAgB;AACzC,WAAK,gBAAgB,mBAAmB,KAAK,OAAO;AAAA,IACtD;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,WAAW,IAAI,GAAG,QAAQ;AAC/B,QAAI,OAAO,cAAc,UAAU;AACjC,aAAO,KAAK,gBAAgB,aAAa,WAAW,GAAG,kCAClD,KAAK,eACL,OACJ;AAAA,IACH,OAAO;AACL,aAAO,KAAK,gBAAgB,aAAa,GAAG,GAAG,kCAC1C,KAAK,eACL,UACJ;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,GAAG,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,IACzD,WAAW,CAAC,GAAG,YAAY;AAAA,IAC3B,UAAU;AAAA,IACV,cAAc,SAAS,uBAAuB,IAAI,KAAK;AACrD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,wBAAwB,IAAI,SAAS;AAAA,MACtD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO,CAAC,GAAG,kBAAkB,OAAO;AAAA,MACpC,WAAW,CAAC,GAAG,sBAAsB,WAAW;AAAA,MAChD,UAAU,CAAC,GAAG,qBAAqB,UAAU;AAAA,MAC7C,QAAQ,CAAC,GAAG,mBAAmB,QAAQ;AAAA,MACvC,WAAW,CAAC,GAAG,sBAAsB,WAAW;AAAA,MAChD,UAAU,CAAC,GAAG,qBAAqB,UAAU;AAAA,MAC7C,SAAS,CAAC,GAAG,oBAAoB,SAAS;AAAA,IAC5C;AAAA,IACA,UAAU,CAAC,WAAW;AAAA,EACxB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,gCAAgC;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;;;ACrrBH,IAAM,uBAAuB;AAAA,EAC3B,SAAS;AACX;AAMA,IAAM,0BAA0B,CAAC,SAAS,aAAa,cAAc,YAAY;AAEjF,IAAM,yBAAyB;AAE/B,IAAM,qBAAqB;AAE3B,IAAM,oBAAoB;AAE1B,IAAM,oBAAoB;AAS1B,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,OAAO,QAAQ;AAAA,EAC3B,sBAAsB,oBAAoB;AAAA,EAC1C,uBAAuB,OAAO,2BAA2B;AAAA,IACvD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,OAAO,QAAQ;AAAA,EAC3B,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA,SAAS,oBAAI,IAAI;AAAA,EACjB,cAAc;AACZ,UAAM,WAAW,OAAO,gBAAgB,EAAE,eAAe,MAAM,IAAI;AACnE,SAAK,iBAAiB,KAAK,QAAQ,kBAAkB,MAAM,wBAAwB,IAAI,UAAQ,SAAS,OAAO,KAAK,WAAW,MAAM,KAAK,gBAAgB,oBAAoB,CAAC,CAAC;AAAA,EAClL;AAAA,EACA,cAAc;AACZ,UAAM,QAAQ,KAAK,OAAO,KAAK;AAC/B,eAAW,QAAQ,OAAO;AACxB,WAAK,cAAc,IAAI;AAAA,IACzB;AACA,SAAK,eAAe,QAAQ,aAAW,QAAQ,CAAC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,MAAM,QAAQ;AAE5B,SAAK,aAAa,wBAAwB,KAAK,sBAAsB,aAAa,EAAE;AAEpF,QAAI,OAAO,aAAa,CAAC,KAAK,aAAa,kBAAkB,GAAG;AAC9D,WAAK,aAAa,oBAAoB,OAAO,aAAa,EAAE;AAAA,IAC9D;AAEA,QAAI,OAAO,UAAU;AACnB,WAAK,aAAa,mBAAmB,EAAE;AAAA,IACzC;AACA,QAAI,OAAO,UAAU;AACnB,WAAK,aAAa,mBAAmB,EAAE;AAAA,IACzC;AAAA,EACF;AAAA;AAAA,EAEA,YAAY,MAAM,UAAU;AAC1B,UAAM,SAAS,KAAK,OAAO,IAAI,IAAI;AAEnC,QAAI,QAAQ;AACV,aAAO,OAAO,iBAAiB;AAC/B,UAAI,CAAC,YAAY,CAAC,OAAO,gBAAgB;AACvC,eAAO,iBAAiB;AACxB,eAAO,SAAS,mBAAmB,IAAI;AAAA,MACzC;AAAA,IACF,WAAW,UAAU;AAGnB,WAAK,aAAa,mBAAmB,EAAE;AAAA,IACzC,OAAO;AACL,WAAK,gBAAgB,iBAAiB;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,WAAS;AACxB,UAAM,cAAc,gBAAgB,KAAK;AACzC,QAAI,uBAAuB,aAAa;AAEtC,YAAM,UAAU,YAAY,QAAQ,IAAI,sBAAsB,KAAK,KAAK,sBAAsB,aAAa,EAAE,IAAI;AACjH,UAAI,SAAS;AACX,aAAK,cAAc,OAAO;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,MAAM;AAClB,QAAI,CAAC,KAAK,aAAa,KAAK,OAAO,IAAI,IAAI,GAAG;AAC5C;AAAA,IACF;AAEA,SAAK,cAAc,aAAa,GAAG,OAAO;AAC1C,UAAM,WAAW,KAAK,UAAU,cAAc,MAAM;AACpD,aAAS,UAAU,IAAI,cAAc,KAAK,aAAa,kBAAkB,CAAC;AAC1E,SAAK,OAAO,QAAQ;AACpB,UAAM,gBAAgB,KAAK;AAC3B,UAAM,gBAAgB,KAAK,sBAAsB,IAAI,eAAe,WAAW,iBAAiB,6BAA6B;AAC7H,UAAM,eAAe,KAAK,sBAAsB,IAAI,eAAe,WAAW,gBAAgB,6BAA6B;AAC3H,UAAM,SAAS;AAAA,MACb,gBAAgB,KAAK,uBAAuB,eAAe,YAAY,KAAK,aAAa,iBAAiB;AAAA,MAC1G,cAAc;AAAA,QACZ,UAAU,KAAK,aAAa,iBAAiB;AAAA,QAC7C,sBAAsB,eAAe;AAAA,QACrC,WAAW;AAAA,UACT;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,WAAW,IAAI,eAAe,QAAQ,KAAK,SAAS,UAAU,KAAK,WAAW,KAAK,SAAS;AAClG,UAAM,iBAAiB,CAAC,OAAO;AAC/B,QAAI,gBAAgB;AAClB,eAAS,mBAAmB,IAAI;AAAA,IAClC;AACA,SAAK,OAAO,IAAI,MAAM;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,SAAK,gBAAgB,sBAAsB;AAAA,EAC7C;AAAA,EACA,cAAc,MAAM;AAClB,UAAM,SAAS,KAAK,OAAO,IAAI,IAAI;AACnC,QAAI,QAAQ;AACV,aAAO,SAAS,qBAAqB;AACrC,WAAK,OAAO,OAAO,IAAI;AAAA,IACzB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,IACzB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;;;AC/JH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,IACjC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAAA,IAAC;AAAA,IAC9D,QAAQ,CAAC,8jBAAgkB;AAAA,IACzkB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,8jBAAgkB;AAAA,IAC3kB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC7BH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,SAAS;AAAA,IACpC,SAAS,CAAC,WAAW,eAAe;AAAA,EACtC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,eAAe;AAAA,EAC5C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,SAAS;AAAA,MACpC,SAAS,CAAC,WAAW,eAAe;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["RippleState"]}